"use client"

import Link from "next/link"
import { useSession, signOut } from "next-auth/react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { 
  Heart, 
  User, 
  Menu, 
  Search, 
  Shield, 
  Settings,
  ChevronDown,
  X,
  PawPrint,
  Home,
  Users,
  BookOpen,
  DollarSign,
  MapPin,
  Phone,
  Mail,
  Calendar,
  Award,
  Sparkles
} from "lucide-react"
import { useState, useRef, useEffect } from "react"
import { UserRole } from "@prisma/client"

interface DropdownItem {
  label: string
  href: string
  description?: string
  icon?: React.ReactNode
  badge?: string
}

interface NavigationItem {
  label: string
  href?: string
  dropdown?: DropdownItem[]
  icon?: React.ReactNode
}

export function EnhancedHeader() {
  const { data: session, status } = useSession()
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null)
  const [searchQuery, setSearchQuery] = useState("")
  const [searchFocused, setSearchFocused] = useState(false)
  const searchRef = useRef<HTMLInputElement>(null)
  const dropdownTimeoutRef = useRef<NodeJS.Timeout>()

  // Navigation structure inspired by Petfinder
  const navigationItems: NavigationItem[] = [
    {
      label: "Find Pets",
      dropdown: [
        {
          label: "Browse All Pets",
          href: "/pets",
          description: "View all available pets for adoption",
          icon: <PawPrint className="h-4 w-4" />
        },
        {
          label: "Dogs",
          href: "/pets?type=dog",
          description: "Find your perfect canine companion",
          icon: <span className="text-lg">🐕</span>
        },
        {
          label: "Cats",
          href: "/pets?type=cat", 
          description: "Discover loving feline friends",
          icon: <span className="text-lg">🐱</span>
        },
        {
          label: "Other Pets",
          href: "/pets?type=other",
          description: "Rabbits, birds, and more",
          icon: <span className="text-lg">🐰</span>
        },
        {
          label: "Recently Added",
          href: "/pets?sort=newest",
          description: "See our newest arrivals",
          icon: <Sparkles className="h-4 w-4" />,
          badge: "New"
        }
      ]
    },
    {
      label: "Get Involved",
      dropdown: [
        {
          label: "Foster a Pet",
          href: "/foster",
          description: "Provide temporary loving care",
          icon: <Home className="h-4 w-4" />
        },
        {
          label: "Volunteer",
          href: "/volunteer", 
          description: "Join our volunteer community",
          icon: <Users className="h-4 w-4" />
        },
        {
          label: "Donate",
          href: "/donate",
          description: "Support our mission",
          icon: <DollarSign className="h-4 w-4" />
        },
        {
          label: "Events",
          href: "/events",
          description: "Upcoming adoption events",
          icon: <Calendar className="h-4 w-4" />
        }
      ]
    },
    {
      label: "Resources",
      dropdown: [
        {
          label: "Pet Care Guide",
          href: "/pet-care",
          description: "Essential pet care information",
          icon: <BookOpen className="h-4 w-4" />
        },
        {
          label: "Blog",
          href: "/blog",
          description: "Stories, tips, and updates",
          icon: <BookOpen className="h-4 w-4" />
        },
        {
          label: "Track Your Pet",
          href: "/track",
          description: "Track adoption progress",
          icon: <MapPin className="h-4 w-4" />
        },
        {
          label: "Success Stories",
          href: "/success-stories",
          description: "Happy adoption tales",
          icon: <Award className="h-4 w-4" />
        }
      ]
    },
    {
      label: "About",
      dropdown: [
        {
          label: "Our Mission",
          href: "/about",
          description: "Learn about our cause",
          icon: <Heart className="h-4 w-4" />
        },
        {
          label: "Contact Us",
          href: "/contact",
          description: "Get in touch",
          icon: <Phone className="h-4 w-4" />
        },
        {
          label: "Locations",
          href: "/locations",
          description: "Find us near you",
          icon: <MapPin className="h-4 w-4" />
        }
      ]
    }
  ]

  // Helper functions
  const hasAdminAccess = () => {
    return session?.user?.role && [UserRole.ADMIN, UserRole.STAFF].includes(session.user.role as UserRole)
  }

  const isAdmin = () => {
    return session?.user?.role === UserRole.ADMIN
  }

  // Dropdown handlers
  const handleMouseEnter = (label: string) => {
    if (dropdownTimeoutRef.current) {
      clearTimeout(dropdownTimeoutRef.current)
    }
    setActiveDropdown(label)
  }

  const handleMouseLeave = () => {
    dropdownTimeoutRef.current = setTimeout(() => {
      setActiveDropdown(null)
    }, 150)
  }

  // Search functionality
  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (searchQuery.trim()) {
      window.location.href = `/pets?search=${encodeURIComponent(searchQuery.trim())}`
    }
  }

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        setActiveDropdown(null)
        setIsMenuOpen(false)
      }
      if (e.key === '/' && e.ctrlKey) {
        e.preventDefault()
        searchRef.current?.focus()
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [])

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-white/95 backdrop-blur-md supports-[backdrop-filter]:bg-white/90 shadow-sm">
      <div className="container mx-auto px-4">
        <div className="flex h-16 items-center justify-between">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-3 group">
            <div className="relative">
              <Heart className="h-8 w-8 text-blue-600 group-hover:text-blue-700 transition-colors" />
              <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
            </div>
            <div className="flex flex-col">
              <span className="text-xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors">PetAdopt</span>
              <span className="text-xs text-gray-500 hidden sm:block">Find Your Perfect Companion</span>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex items-center space-x-1">
            {navigationItems.map((item) => (
              <div
                key={item.label}
                className="relative"
                onMouseEnter={() => handleMouseEnter(item.label)}
                onMouseLeave={handleMouseLeave}
              >
                {item.href ? (
                  <Link
                    href={item.href}
                    className="flex items-center px-4 py-2 text-sm font-medium text-gray-700 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-all duration-200"
                  >
                    {item.icon}
                    <span className="ml-1">{item.label}</span>
                  </Link>
                ) : (
                  <button
                    className="flex items-center px-4 py-2 text-sm font-medium text-gray-700 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-all duration-200"
                    aria-expanded={activeDropdown === item.label}
                    aria-haspopup="true"
                  >
                    {item.icon}
                    <span className="ml-1">{item.label}</span>
                    <ChevronDown className={`ml-1 h-4 w-4 transition-transform duration-200 ${
                      activeDropdown === item.label ? 'rotate-180' : ''
                    }`} />
                  </button>
                )}

                {/* Dropdown Menu */}
                {item.dropdown && activeDropdown === item.label && (
                  <div className="absolute top-full left-0 mt-1 w-80 bg-white rounded-xl shadow-xl border border-gray-100 py-2 z-50 animate-in slide-in-from-top-2 duration-200">
                    {item.dropdown.map((dropdownItem, index) => (
                      <Link
                        key={index}
                        href={dropdownItem.href}
                        className="flex items-start px-4 py-3 hover:bg-gray-50 transition-colors group"
                        onClick={() => setActiveDropdown(null)}
                      >
                        <div className="flex-shrink-0 mt-0.5 text-gray-400 group-hover:text-blue-600 transition-colors">
                          {dropdownItem.icon}
                        </div>
                        <div className="ml-3 flex-1">
                          <div className="flex items-center">
                            <span className="text-sm font-medium text-gray-900 group-hover:text-blue-600 transition-colors">
                              {dropdownItem.label}
                            </span>
                            {dropdownItem.badge && (
                              <span className="ml-2 px-2 py-0.5 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
                                {dropdownItem.badge}
                              </span>
                            )}
                          </div>
                          {dropdownItem.description && (
                            <p className="text-xs text-gray-500 mt-0.5">
                              {dropdownItem.description}
                            </p>
                          )}
                        </div>

            {/* User Authentication */}
            {status === "loading" ? (
              <div className="h-8 w-8 animate-pulse bg-gray-200 rounded-full" />
            ) : session ? (
              <div className="flex items-center space-x-2">
                {/* Favorites */}
                <Link href="/favorites">
                  <Button variant="ghost" size="icon" className="relative hover:bg-red-50 hover:text-red-600 transition-colors">
                    <Heart className="h-4 w-4" />
                    <span className="sr-only">Favorites</span>
                  </Button>
                </Link>

                {/* User Menu */}
                <div className="relative">
                  <Button
                    variant="ghost"
                    className="flex items-center space-x-2 hover:bg-blue-50 hover:text-blue-600 transition-colors rounded-full px-3"
                    onClick={() => setIsMenuOpen(!isMenuOpen)}
                  >
                    <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-sm font-medium">
                      {session.user?.name?.charAt(0)?.toUpperCase() || session.user?.email?.charAt(0)?.toUpperCase() || 'U'}
                    </div>
                    <span className="hidden sm:block text-sm font-medium">
                      {session.user?.name?.split(' ')[0] || 'User'}
                    </span>
                    <ChevronDown className="h-4 w-4" />
                  </Button>

                  {isMenuOpen && (
                    <div className="absolute right-0 mt-2 w-64 bg-white rounded-xl shadow-xl border border-gray-100 py-2 z-50 animate-in slide-in-from-top-2 duration-200">
                      {/* User Info */}
                      <div className="px-4 py-3 border-b border-gray-100">
                        <div className="flex items-center space-x-3">
                          <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-medium">
                            {session.user?.name?.charAt(0)?.toUpperCase() || session.user?.email?.charAt(0)?.toUpperCase() || 'U'}
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium text-gray-900 truncate">
                              {session.user?.name || 'User'}
                            </p>
                            <p className="text-xs text-gray-500 truncate">
                              {session.user?.email}
                            </p>
                          </div>
                        </div>
                      </div>

                      {/* Menu Items */}
                      <div className="py-1">
                        <Link
                          href="/dashboard"
                          className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                          onClick={() => setIsMenuOpen(false)}
                        >
                          <User className="h-4 w-4 mr-3 text-gray-400" />
                          Dashboard
                        </Link>
                        <Link
                          href="/profile"
                          className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                          onClick={() => setIsMenuOpen(false)}
                        >
                          <Settings className="h-4 w-4 mr-3 text-gray-400" />
                          Profile Settings
                        </Link>
                        <Link
                          href="/applications"
                          className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                          onClick={() => setIsMenuOpen(false)}
                        >
                          <BookOpen className="h-4 w-4 mr-3 text-gray-400" />
                          My Applications
                        </Link>
                        <Link
                          href="/favorites"
                          className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                          onClick={() => setIsMenuOpen(false)}
                        >
                          <Heart className="h-4 w-4 mr-3 text-gray-400" />
                          Saved Pets
                        </Link>

                        {/* Admin/Staff Access */}
                        {hasAdminAccess() && (
                          <>
                            <hr className="my-1" />
                            <Link
                              href="/admin"
                              className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                              onClick={() => setIsMenuOpen(false)}
                            >
                              <Shield className="h-4 w-4 mr-3 text-blue-500" />
                              <span>Admin Panel</span>
                              <span className="ml-auto px-2 py-0.5 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
                                Staff
                              </span>
                            </Link>
                          </>
                        )}

                        {/* User Management - Admin Only */}
                        {isAdmin() && (
                          <Link
                            href="/admin/users"
                            className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                            onClick={() => setIsMenuOpen(false)}
                          >
                            <Users className="h-4 w-4 mr-3 text-purple-500" />
                            <span>User Management</span>
                            <span className="ml-auto px-2 py-0.5 text-xs font-medium bg-purple-100 text-purple-800 rounded-full">
                              Admin
                            </span>
                          </Link>
                        )}

                        <hr className="my-1" />
                        <button
                          onClick={() => {
                            signOut()
                            setIsMenuOpen(false)
                          }}
                          className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors"
                        >
                          <span className="mr-3">🚪</span>
                          Sign Out
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ) : (
              <div className="flex items-center space-x-2">
                <Link href="/auth/signin">
                  <Button variant="ghost" className="hover:bg-blue-50 hover:text-blue-600 transition-colors">
                    Sign In
                  </Button>
                </Link>
                <Link href="/auth/signup">
                  <Button className="bg-blue-600 hover:bg-blue-700 text-white rounded-full px-6 shadow-lg hover:shadow-xl transition-all duration-200">
                    Get Started
                  </Button>
                </Link>
              </div>
            )}

            {/* Mobile Menu Button */}
            <Button
              variant="ghost"
              size="icon"
              className="lg:hidden hover:bg-gray-100 transition-colors"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              aria-label="Toggle mobile menu"
            >
              {isMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
            </Button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="lg:hidden border-t border-gray-100 py-4 animate-in slide-in-from-top-2 duration-200">
            {/* Mobile Search */}
            <div className="mb-4">
              <form onSubmit={handleSearchSubmit} className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  type="text"
                  placeholder="Search pets..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 pr-4 py-2 bg-gray-50 border-gray-200 focus:bg-white focus:border-blue-300 rounded-full w-full"
                />
              </form>
            </div>

            {/* Mobile Navigation Items */}
            <nav className="space-y-1">
              {navigationItems.map((item) => (
                <div key={item.label}>
                  {item.href ? (
                    <Link
                      href={item.href}
                      className="flex items-center px-4 py-3 text-sm font-medium text-gray-700 hover:bg-gray-50 hover:text-blue-600 transition-colors rounded-lg"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      {item.icon}
                      <span className="ml-3">{item.label}</span>
                    </Link>
                  ) : (
                    <>
                      <button
                        className="flex items-center justify-between w-full px-4 py-3 text-sm font-medium text-gray-700 hover:bg-gray-50 hover:text-blue-600 transition-colors rounded-lg"
                        onClick={() => setActiveDropdown(activeDropdown === item.label ? null : item.label)}
                      >
                        <div className="flex items-center">
                          {item.icon}
                          <span className="ml-3">{item.label}</span>
                        </div>
                        <ChevronDown className={`h-4 w-4 transition-transform duration-200 ${
                          activeDropdown === item.label ? 'rotate-180' : ''
                        }`} />
                      </button>

                      {/* Mobile Dropdown */}
                      {item.dropdown && activeDropdown === item.label && (
                        <div className="ml-4 mt-1 space-y-1 animate-in slide-in-from-top-1 duration-150">
                          {item.dropdown.map((dropdownItem, index) => (
                            <Link
                              key={index}
                              href={dropdownItem.href}
                              className="flex items-center px-4 py-2 text-sm text-gray-600 hover:bg-gray-50 hover:text-blue-600 transition-colors rounded-lg"
                              onClick={() => {
                                setIsMenuOpen(false)
                                setActiveDropdown(null)
                              }}
                            >
                              <div className="flex-shrink-0 text-gray-400">
                                {dropdownItem.icon}
                              </div>
                              <span className="ml-3">{dropdownItem.label}</span>
                              {dropdownItem.badge && (
                                <span className="ml-auto px-2 py-0.5 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
                                  {dropdownItem.badge}
                                </span>
                              )}
                            </Link>
                          ))}
                        </div>
                      )}
                    </>
                  )}
                </div>
              ))}
            </nav>
          </div>
        )}
      </div>
    </header>
  )
}
                      </Link>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </nav>

          {/* Search and Actions */}
          <div className="flex items-center space-x-3">
            {/* Enhanced Search */}
            <div className="hidden md:block relative">
              <form onSubmit={handleSearchSubmit} className="relative">
                <div className={`relative transition-all duration-300 ${
                  searchFocused ? 'w-80' : 'w-64'
                }`}>
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    ref={searchRef}
                    type="text"
                    placeholder="Search pets by name, breed, or location..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    onFocus={() => setSearchFocused(true)}
                    onBlur={() => setSearchFocused(false)}
                    className="pl-10 pr-4 py-2 bg-gray-50 border-gray-200 focus:bg-white focus:border-blue-300 focus:ring-2 focus:ring-blue-100 rounded-full transition-all duration-200"
                  />
                  {searchQuery && (
                    <button
                      type="button"
                      onClick={() => setSearchQuery("")}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                    >
                      <X className="h-4 w-4" />
                    </button>
                  )}
                </div>
              </form>
            </div>
