import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const trackingNumber = searchParams.get("trackingNumber")

    if (!trackingNumber) {
      return NextResponse.json(
        { error: "Tracking number is required" },
        { status: 400 }
      )
    }

    // Find the transport record by tracking number
    const transportRecord = await prisma.transport.findUnique({
      where: { trackingNumber },
      include: {
        pet: {
          select: {
            id: true,
            name: true,
            species: true,
            breed: true,
            age: true,
            size: true,
            photos: {
              select: {
                url: true,
                isPrimary: true
              },
              orderBy: { order: "asc" }
            }
          }
        },
        requestedBy: {
          select: {
            name: true,
            email: true
          }
        },
        assignedVolunteer: {
          select: {
            name: true,
            phone: true
          }
        }
      }
    })

    if (!transportRecord) {
      return NextResponse.json(
        { error: "Tracking number not found" },
        { status: 404 }
      )
    }

    // Get tracking updates/history
    const trackingUpdates = await prisma.trackingUpdate.findMany({
      where: { transportId: transportRecord.id },
      orderBy: { createdAt: "desc" }
    })

    // Calculate progress percentage based on status
    let progressPercentage = 0
    switch (transportRecord.status) {
      case "REQUESTED":
        progressPercentage = 10
        break
      case "ASSIGNED":
        progressPercentage = 25
        break
      case "IN_PROGRESS":
        progressPercentage = 50
        break
      case "PICKED_UP":
        progressPercentage = 75
        break
      case "DELIVERED":
        progressPercentage = 100
        break
      case "CANCELLED":
        progressPercentage = 0
        break
      default:
        progressPercentage = 0
    }

    // Format the response with public-safe information
    const trackingInfo = {
      trackingNumber: transportRecord.trackingNumber,
      status: transportRecord.status,
      progressPercentage,
      pet: {
        name: transportRecord.pet.name,
        species: transportRecord.pet.species,
        breed: transportRecord.pet.breed,
        age: transportRecord.pet.age,
        size: transportRecord.pet.size,
        photo: transportRecord.pet.photos.find(p => p.isPrimary)?.url || 
               transportRecord.pet.photos[0]?.url || null
      },
      journey: {
        origin: transportRecord.pickupLocation,
        destination: transportRecord.dropoffLocation,
        scheduledDate: transportRecord.scheduledDate,
        estimatedArrival: transportRecord.estimatedArrival,
        actualPickupTime: transportRecord.actualPickupTime,
        actualDeliveryTime: transportRecord.actualDeliveryTime,
        estimatedDistance: transportRecord.estimatedDistance,
        estimatedDuration: transportRecord.estimatedDuration
      },
      driver: transportRecord.assignedVolunteer ? {
        name: transportRecord.assignedVolunteer.name,
        phone: transportRecord.assignedVolunteer.phone
      } : null,
      updates: trackingUpdates.map(update => ({
        id: update.id,
        status: update.status,
        message: update.message,
        location: update.location,
        timestamp: update.createdAt,
        coordinates: update.latitude && update.longitude ? {
          lat: update.latitude,
          lng: update.longitude
        } : null
      })),
      lastUpdated: transportRecord.updatedAt
    }

    return NextResponse.json(trackingInfo)

  } catch (error) {
    console.error("Error fetching tracking information:", error)
    return NextResponse.json(
      { error: "Failed to fetch tracking information" },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { trackingNumber, status, message, location, latitude, longitude } = body

    if (!trackingNumber) {
      return NextResponse.json(
        { error: "Tracking number is required" },
        { status: 400 }
      )
    }

    // Find the transport record
    const transportRecord = await prisma.transport.findUnique({
      where: { trackingNumber }
    })

    if (!transportRecord) {
      return NextResponse.json(
        { error: "Tracking number not found" },
        { status: 404 }
      )
    }

    // Create tracking update
    const trackingUpdate = await prisma.trackingUpdate.create({
      data: {
        transportId: transportRecord.id,
        status: status || transportRecord.status,
        message: message || `Status updated to ${status || transportRecord.status}`,
        location: location || null,
        latitude: latitude || null,
        longitude: longitude || null
      }
    })

    // Update transport status if provided
    if (status && status !== transportRecord.status) {
      await prisma.transport.update({
        where: { id: transportRecord.id },
        data: { 
          status,
          ...(status === "PICKED_UP" && { actualPickupTime: new Date() }),
          ...(status === "DELIVERED" && { actualDeliveryTime: new Date() })
        }
      })
    }

    return NextResponse.json({
      message: "Tracking update added successfully",
      update: trackingUpdate
    }, { status: 201 })

  } catch (error) {
    console.error("Error creating tracking update:", error)
    return NextResponse.json(
      { error: "Failed to create tracking update" },
      { status: 500 }
    )
  }
}
