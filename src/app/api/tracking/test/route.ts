import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"

export async function POST(request: NextRequest) {
  try {
    // This is a test endpoint to create sample tracking data
    // In production, this would be removed or protected
    
    // First, let's find a pet and user to use for testing
    const pet = await prisma.pet.findFirst()
    const user = await prisma.user.findFirst()
    
    if (!pet || !user) {
      return NextResponse.json(
        { error: "No pets or users found in database" },
        { status: 400 }
      )
    }

    // Create a sample transport record
    const transport = await prisma.transport.create({
      data: {
        trackingNumber: "TRK" + Math.random().toString(36).substr(2, 6).toUpperCase(),
        petId: pet.id,
        requestedById: user.id,
        transportType: "ADOPTION",
        priority: "NORMAL",
        status: "IN_PROGRESS",
        pickupLocation: "Downtown Animal Shelter",
        pickupAddress: "123 Main St",
        pickupCity: "Anytown",
        pickupState: "CA",
        pickupZip: "12345",
        dropoffLocation: "Foster Home",
        dropoffAddress: "456 Oak Ave",
        dropoffCity: "Somewhere",
        dropoffState: "CA",
        dropoffZip: "67890",
        scheduledDate: new Date(),
        estimatedArrival: new Date(Date.now() + 2 * 60 * 60 * 1000), // 2 hours from now
        estimatedDistance: 25.5,
        estimatedDuration: 120,
        specialInstructions: "Handle with care - nervous pet",
        emergencyContact: "Emergency Contact",
        emergencyPhone: "(*************"
      }
    })

    // Create some tracking updates
    const updates = await Promise.all([
      prisma.trackingUpdate.create({
        data: {
          transportId: transport.id,
          status: "PICKED_UP",
          message: "Pet picked up from shelter",
          location: "Downtown Animal Shelter",
          latitude: 37.7749,
          longitude: -122.4194,
          temperature: 72.0,
          petCondition: "GOOD"
        }
      }),
      prisma.trackingUpdate.create({
        data: {
          transportId: transport.id,
          status: "IN_TRANSIT",
          message: "On route to destination",
          location: "Highway 101",
          latitude: 37.7849,
          longitude: -122.4094,
          temperature: 71.0,
          petCondition: "GOOD"
        }
      }),
      prisma.trackingUpdate.create({
        data: {
          transportId: transport.id,
          status: "IN_TRANSIT",
          message: "Rest stop - pet is doing well",
          location: "Rest Area Mile 15",
          latitude: 37.7949,
          longitude: -122.3994,
          temperature: 70.0,
          petCondition: "GOOD"
        }
      })
    ])

    return NextResponse.json({
      message: "Sample transport data created successfully",
      transport: {
        trackingNumber: transport.trackingNumber,
        petName: pet.name,
        status: transport.status
      },
      updatesCount: updates.length
    })

  } catch (error) {
    console.error("Error creating sample transport data:", error)
    return NextResponse.json(
      { error: "Failed to create sample data" },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    // Get all transport records for testing
    const transports = await prisma.transport.findMany({
      include: {
        pet: {
          select: {
            name: true,
            species: true,
            breed: true
          }
        },
        trackingUpdates: {
          orderBy: { createdAt: "desc" },
          take: 3
        }
      },
      orderBy: { createdAt: "desc" },
      take: 10
    })

    return NextResponse.json({
      message: "Available tracking numbers for testing",
      transports: transports.map(t => ({
        trackingNumber: t.trackingNumber,
        petName: t.pet.name,
        status: t.status,
        updatesCount: t.trackingUpdates.length
      }))
    })

  } catch (error) {
    console.error("Error fetching transport data:", error)
    return NextResponse.json(
      { error: "Failed to fetch transport data" },
      { status: 500 }
    )
  }
}
