"use client"

import { useState } from "react"
import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  Heart, 
  Home, 
  Users, 
  Clock, 
  Shield, 
  CheckCircle,
  ArrowRight,
  User,
  Phone,
  Mail
} from "lucide-react"
import Link from "next/link"
import { toast } from "react-hot-toast"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { fosterApplicationSchema, type FosterApplicationInput } from "@/lib/validations"

export default function FosterPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [showApplication, setShowApplication] = useState(false)
  const [loading, setLoading] = useState(false)

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm<FosterApplicationInput>({
    resolver: zodResolver(fosterApplicationSchema),
    defaultValues: {
      maxPets: 1,
      preferredSpecies: [],
      preferredAges: [],
      preferredSizes: [],
      emergencyFoster: false,
      longTermFoster: true,
      shortTermFoster: true,
    },
  })

  const watchedValues = watch()

  const onSubmit = async (data: FosterApplicationInput) => {
    if (!session) {
      toast.error("Please sign in to submit a foster application")
      return
    }

    setLoading(true)
    try {
      const response = await fetch("/api/foster/applications", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      })

      if (response.ok) {
        toast.success("Foster application submitted successfully!")
        router.push("/dashboard")
      } else {
        const errorData = await response.json()
        toast.error(errorData.error || "Failed to submit application")
      }
    } catch (error) {
      toast.error("Something went wrong. Please try again.")
    } finally {
      setLoading(false)
    }
  }

  return (
    <div
      className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100"
      style={{
        backgroundImage: "url('/images/FOSTER FAMILY.jpg'), linear-gradient(to bottom right, rgb(239 246 255), rgb(224 231 255))",
        backgroundSize: 'cover, cover',
        backgroundPosition: 'center, center',
        backgroundRepeat: 'no-repeat, no-repeat',
        backgroundAttachment: 'fixed, scroll'
      }}
    >
      {/* Overlay for better content readability */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50/50 to-indigo-100/50"></div>
      <div className="relative z-10 min-h-screen">
      <div className="container mx-auto px-4 py-12">
        {!showApplication ? (
          <>
            {/* Hero Section */}
            <div className="text-center mb-16">
              <div className="inline-flex items-center justify-center w-20 h-20 bg-blue-100 rounded-full mb-6">
                <Home className="h-10 w-10 text-blue-600" />
              </div>
              <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                Become a Foster Family
              </h1>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
                Open your heart and home to pets in need. Foster care saves lives by providing temporary, 
                loving homes while pets await their forever families.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                {session ? (
                  <Button 
                    size="lg" 
                    onClick={() => setShowApplication(true)}
                    className="bg-blue-600 hover:bg-blue-700"
                  >
                    Apply to Foster
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                ) : (
                  <Link href="/auth/signin?callbackUrl=/foster">
                    <Button size="lg" className="bg-blue-600 hover:bg-blue-700">
                      Sign In to Apply
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  </Link>
                )}
                <Button variant="outline" size="lg">
                  Learn More
                </Button>
              </div>
            </div>

            {/* Benefits Section */}
            <div className="grid md:grid-cols-3 gap-8 mb-16">
              <Card className="text-center">
                <CardHeader>
                  <div className="mx-auto mb-4 p-4 bg-red-100 rounded-full w-16 h-16 flex items-center justify-center">
                    <Heart className="h-8 w-8 text-red-600" />
                  </div>
                  <CardTitle>Save Lives</CardTitle>
                  <CardDescription>
                    Every foster home saves a life and makes room for another rescue
                  </CardDescription>
                </CardHeader>
              </Card>

              <Card className="text-center">
                <CardHeader>
                  <div className="mx-auto mb-4 p-4 bg-green-100 rounded-full w-16 h-16 flex items-center justify-center">
                    <Users className="h-8 w-8 text-green-600" />
                  </div>
                  <CardTitle>Build Community</CardTitle>
                  <CardDescription>
                    Join a network of caring foster families and volunteers
                  </CardDescription>
                </CardHeader>
              </Card>

              <Card className="text-center">
                <CardHeader>
                  <div className="mx-auto mb-4 p-4 bg-purple-100 rounded-full w-16 h-16 flex items-center justify-center">
                    <Shield className="h-8 w-8 text-purple-600" />
                  </div>
                  <CardTitle>Full Support</CardTitle>
                  <CardDescription>
                    We provide food, medical care, and 24/7 support
                  </CardDescription>
                </CardHeader>
              </Card>
            </div>

            {/* Foster Types */}
            <div className="mb-16">
              <h2 className="text-3xl font-bold text-center text-gray-900 mb-8">
                Types of Foster Care
              </h2>
              <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
                <Card>
                  <CardHeader>
                    <Clock className="h-8 w-8 text-blue-600 mb-2" />
                    <CardTitle className="text-lg">Short-term</CardTitle>
                    <CardDescription>
                      1-4 weeks for pets recovering from surgery or illness
                    </CardDescription>
                  </CardHeader>
                </Card>

                <Card>
                  <CardHeader>
                    <Home className="h-8 w-8 text-green-600 mb-2" />
                    <CardTitle className="text-lg">Long-term</CardTitle>
                    <CardDescription>
                      Several months for pets needing behavioral training
                    </CardDescription>
                  </CardHeader>
                </Card>

                <Card>
                  <CardHeader>
                    <Heart className="h-8 w-8 text-red-600 mb-2" />
                    <CardTitle className="text-lg">Emergency</CardTitle>
                    <CardDescription>
                      Immediate care for pets in crisis situations
                    </CardDescription>
                  </CardHeader>
                </Card>

                <Card>
                  <CardHeader>
                    <Users className="h-8 w-8 text-purple-600 mb-2" />
                    <CardTitle className="text-lg">Maternity</CardTitle>
                    <CardDescription>
                      Pregnant mothers and newborn litters
                    </CardDescription>
                  </CardHeader>
                </Card>
              </div>
            </div>

            {/* Requirements */}
            <Card className="mb-16">
              <CardHeader>
                <CardTitle className="text-2xl text-center">Foster Requirements</CardTitle>
                <CardDescription className="text-center">
                  Basic requirements to become a foster family
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div className="flex items-center">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                      <span>Be at least 18 years old</span>
                    </div>
                    <div className="flex items-center">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                      <span>Have reliable transportation</span>
                    </div>
                    <div className="flex items-center">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                      <span>Commit to foster orientation</span>
                    </div>
                    <div className="flex items-center">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                      <span>Allow home visit inspection</span>
                    </div>
                  </div>
                  <div className="space-y-4">
                    <div className="flex items-center">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                      <span>Provide safe, loving environment</span>
                    </div>
                    <div className="flex items-center">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                      <span>Follow medical care instructions</span>
                    </div>
                    <div className="flex items-center">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                      <span>Communicate regularly with staff</span>
                    </div>
                    <div className="flex items-center">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                      <span>Help with adoption events</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* FAQ */}
            <div className="text-center">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Ready to Make a Difference?
              </h2>
              <p className="text-xl text-gray-600 mb-8">
                Join our foster family network and help save lives today.
              </p>
              {session ? (
                <Button 
                  size="lg" 
                  onClick={() => setShowApplication(true)}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  Start Foster Application
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              ) : (
                <Link href="/auth/signin?callbackUrl=/foster">
                  <Button size="lg" className="bg-blue-600 hover:bg-blue-700">
                    Sign In to Apply
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </Link>
              )}
            </div>
          </>
        ) : (
          /* Foster Application Form */
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-8">
              <h1 className="text-3xl font-bold text-gray-900 mb-4">
                Foster Application
              </h1>
              <p className="text-gray-600">
                Help us match you with the perfect foster pet by telling us about your preferences and experience.
              </p>
            </div>

            <form onSubmit={handleSubmit(onSubmit)}>
              <Card>
                <CardHeader>
                  <CardTitle>Foster Preferences</CardTitle>
                  <CardDescription>
                    Tell us about your fostering preferences and capacity
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Basic Preferences */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Maximum Number of Pets *
                      </label>
                      <Input
                        type="number"
                        min="1"
                        max="10"
                        {...register("maxPets", { valueAsNumber: true })}
                        placeholder="How many pets can you foster?"
                      />
                      {errors.maxPets && (
                        <p className="text-red-500 text-sm mt-1">{errors.maxPets.message}</p>
                      )}
                    </div>
                  </div>

                  {/* Species Preferences */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Preferred Species *
                    </label>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                      {["Dogs", "Cats", "Rabbits", "Birds", "Other"].map((species) => (
                        <label key={species} className="flex items-center">
                          <input
                            type="checkbox"
                            value={species}
                            {...register("preferredSpecies")}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                          />
                          <span className="ml-2 text-sm">{species}</span>
                        </label>
                      ))}
                    </div>
                    {errors.preferredSpecies && (
                      <p className="text-red-500 text-sm mt-1">{errors.preferredSpecies.message}</p>
                    )}
                  </div>

                  {/* Age Preferences */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Preferred Ages *
                    </label>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                      {["Babies", "Young", "Adult", "Senior"].map((age) => (
                        <label key={age} className="flex items-center">
                          <input
                            type="checkbox"
                            value={age}
                            {...register("preferredAges")}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                          />
                          <span className="ml-2 text-sm">{age}</span>
                        </label>
                      ))}
                    </div>
                    {errors.preferredAges && (
                      <p className="text-red-500 text-sm mt-1">{errors.preferredAges.message}</p>
                    )}
                  </div>

                  {/* Foster Types */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Foster Types
                    </label>
                    <div className="space-y-3">
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          {...register("emergencyFoster")}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <span className="ml-2 text-sm">Emergency Foster (available on short notice)</span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          {...register("longTermFoster")}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <span className="ml-2 text-sm">Long-term Foster (several months)</span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          {...register("shortTermFoster")}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <span className="ml-2 text-sm">Short-term Foster (1-4 weeks)</span>
                      </label>
                    </div>
                  </div>

                  {/* Experience */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Foster Experience
                      </label>
                      <textarea
                        {...register("fosterExperience")}
                        className="w-full p-2 border border-gray-300 rounded-md"
                        rows={3}
                        placeholder="Describe any previous fostering experience..."
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Special Needs Experience
                      </label>
                      <textarea
                        {...register("specialNeedsExperience")}
                        className="w-full p-2 border border-gray-300 rounded-md"
                        rows={3}
                        placeholder="Experience with special needs pets..."
                      />
                    </div>
                  </div>

                  {/* Housing Information */}
                  <div className="border-t pt-6">
                    <h3 className="text-lg font-semibold mb-4">Housing Information</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Do you have a yard?
                        </label>
                        <select
                          {...register("hasYard", { 
                            setValueAs: (value) => value === "true" 
                          })}
                          className="w-full p-2 border border-gray-300 rounded-md"
                        >
                          <option value="">Select option</option>
                          <option value="true">Yes</option>
                          <option value="false">No</option>
                        </select>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Is your yard fenced?
                        </label>
                        <select
                          {...register("yardFenced", { 
                            setValueAs: (value) => value === "true" 
                          })}
                          className="w-full p-2 border border-gray-300 rounded-md"
                          disabled={!watchedValues.hasYard}
                        >
                          <option value="">Select option</option>
                          <option value="true">Yes</option>
                          <option value="false">No</option>
                        </select>
                      </div>
                    </div>
                  </div>

                  {/* Emergency Contact */}
                  <div className="border-t pt-6">
                    <h3 className="text-lg font-semibold mb-4">Emergency Contact *</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Name *
                        </label>
                        <Input
                          {...register("emergencyContactName")}
                          placeholder="Emergency contact name"
                        />
                        {errors.emergencyContactName && (
                          <p className="text-red-500 text-sm mt-1">{errors.emergencyContactName.message}</p>
                        )}
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Phone Number *
                        </label>
                        <Input
                          type="tel"
                          {...register("emergencyContactPhone")}
                          placeholder="Emergency contact phone"
                        />
                        {errors.emergencyContactPhone && (
                          <p className="text-red-500 text-sm mt-1">{errors.emergencyContactPhone.message}</p>
                        )}
                      </div>
                    </div>
                  </div>
                </CardContent>

                <div className="flex justify-between items-center p-6 border-t">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setShowApplication(false)}
                  >
                    Back to Information
                  </Button>

                  <Button
                    type="submit"
                    disabled={loading}
                    className="bg-blue-600 hover:bg-blue-700"
                  >
                    {loading ? "Submitting..." : "Submit Foster Application"}
                  </Button>
                </div>
              </Card>
            </form>
          </div>
        )}
      </div>
      </div>
    </div>
  )
}
