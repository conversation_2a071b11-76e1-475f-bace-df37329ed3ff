@import "tailwindcss";

@layer base {
  * {
    @apply border-gray-200;
  }
  body {
    @apply bg-white text-gray-900;
  }
}

@layer utilities {
  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }

  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }

  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }

  /* Enhanced background image display */
  .bg-image-enhanced {
    background-attachment: fixed;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
  }

  /* Mobile optimization for background images */
  @media (max-width: 768px) {
    .bg-image-enhanced {
      background-attachment: scroll;
    }
  }

  /* Improved backdrop blur for better readability */
  .backdrop-blur-enhanced {
    backdrop-filter: blur(8px) saturate(180%);
    -webkit-backdrop-filter: blur(8px) saturate(180%);
  }

  /* Enhanced navigation animations */
  .animate-in {
    animation: slideIn 0.2s ease-out;
  }

  .slide-in-from-top-1 {
    animation: slideInFromTop1 0.15s ease-out;
  }

  .slide-in-from-top-2 {
    animation: slideInFromTop2 0.2s ease-out;
  }

  @keyframes slideIn {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slideInFromTop1 {
    from {
      opacity: 0;
      transform: translateY(-5px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slideInFromTop2 {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Smooth hover transitions for navigation */
  .nav-item {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .nav-item:hover {
    transform: translateY(-1px);
  }

  /* Enhanced dropdown shadows */
  .dropdown-shadow {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }

  /* Search input focus ring */
  .search-focus {
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  /* Mobile menu backdrop */
  .mobile-backdrop {
    backdrop-filter: blur(4px);
    background: rgba(0, 0, 0, 0.1);
  }
}
