import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { AuthProvider } from "@/components/providers/session-provider";
import { EnhancedHeader } from "@/components/layout/enhanced-header";
import { Footer } from "@/components/layout/footer";
import { Toaster } from "react-hot-toast";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "PetAdopt - Find Your Perfect Companion",
  description: "Connect with loving pets in need of homes. Browse adoptable dogs, cats, and other animals from local shelters and rescue organizations.",
  keywords: "pet adoption, animal rescue, dogs, cats, shelter, adopt a pet",
  authors: [{ name: "PetAdopt Team" }],
  openGraph: {
    title: "PetAdopt - Find Your Perfect Companion",
    description: "Connect with loving pets in need of homes. Browse adoptable dogs, cats, and other animals from local shelters and rescue organizations.",
    type: "website",
    locale: "en_US",
  },
  twitter: {
    card: "summary_large_image",
    title: "PetAdopt - Find Your Perfect Companion",
    description: "Connect with loving pets in need of homes. Browse adoptable dogs, cats, and other animals from local shelters and rescue organizations.",
  },
  robots: {
    index: true,
    follow: true,
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <AuthProvider>
          <div className="min-h-screen flex flex-col">
            <EnhancedHeader />
            <main className="flex-1">
              {children}
            </main>
            <Footer />
          </div>
          <Toaster
            position="top-right"
            toastOptions={{
              duration: 4000,
              style: {
                background: '#363636',
                color: '#fff',
              },
            }}
          />
        </AuthProvider>
      </body>
    </html>
  );
}
