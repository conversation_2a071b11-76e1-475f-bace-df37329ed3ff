"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Tabs, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { 
  Heart, 
  Utensils, 
  Stethoscope, 
  Scissors, 
  GraduationCap,
  Home,
  Shield,
  Clock,
  AlertTriangle,
  CheckCircle,
  Phone,
  Calendar,
  BookOpen,
  Play,
  Thermometer,
  Activity,
  Zap,
  Star,
  Download,
  Share2,
  Search,
  Filter,
  ChevronRight,
  Info,
  Users,
  MapPin,
  Camera,
  Video,
  FileText,
  Award,
  Target,
  TrendingUp,
  Calculator
} from "lucide-react"
import { Input } from "@/components/ui/input"
import Link from "next/link"

const careCategories = [
  {
    id: "feeding",
    title: "Feeding & Nutrition",
    icon: Utensils,
    color: "bg-green-100 text-green-800",
    description: "Complete guide to proper pet nutrition and feeding schedules"
  },
  {
    id: "health",
    title: "Health & Medical Care",
    icon: Stethoscope,
    color: "bg-red-100 text-red-800",
    description: "Essential health information and medical care guidelines"
  },
  {
    id: "grooming",
    title: "Grooming & Hygiene",
    icon: Scissors,
    color: "bg-blue-100 text-blue-800",
    description: "Keep your pet clean, healthy, and looking their best"
  },
  {
    id: "training",
    title: "Training & Behavior",
    icon: GraduationCap,
    color: "bg-purple-100 text-purple-800",
    description: "Training tips and behavioral guidance for a well-behaved pet"
  },
  {
    id: "safety",
    title: "Safety & Emergency",
    icon: Shield,
    color: "bg-orange-100 text-orange-800",
    description: "Keep your pet safe and know what to do in emergencies"
  },
  {
    id: "environment",
    title: "Home Environment",
    icon: Home,
    color: "bg-teal-100 text-teal-800",
    description: "Creating the perfect living space for your pet"
  }
]

const quickTips = [
  {
    category: "Daily Care",
    tips: [
      "Fresh water should always be available",
      "Establish regular feeding times",
      "Daily exercise is essential for most pets",
      "Regular grooming prevents matting and health issues"
    ]
  },
  {
    category: "Health Monitoring",
    tips: [
      "Check for changes in appetite or behavior",
      "Monitor weight regularly",
      "Keep vaccination records up to date",
      "Schedule annual vet checkups"
    ]
  },
  {
    category: "Emergency Preparedness",
    tips: [
      "Keep emergency vet contact information handy",
      "Have a first aid kit for pets",
      "Know the signs of common emergencies",
      "Keep important documents accessible"
    ]
  }
]

const emergencyContacts = [
  {
    name: "24/7 Pet Emergency Hotline",
    phone: "(555) 911-PETS",
    description: "Immediate emergency assistance"
  },
  {
    name: "Pet Poison Control",
    phone: "(*************",
    description: "24/7 poison control center"
  },
  {
    name: "Local Emergency Vet",
    phone: "(*************",
    description: "Nearest emergency veterinary clinic"
  }
]

export default function PetCareGuidePage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("all")
  const [activeTab, setActiveTab] = useState("overview")

  const filteredCategories = careCategories.filter(category =>
    (selectedCategory === "all" || category.id === selectedCategory) &&
    (searchTerm === "" || category.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
     category.description.toLowerCase().includes(searchTerm.toLowerCase()))
  )

  return (
    <div
      className="min-h-screen bg-gradient-to-br from-green-50 via-white to-blue-50"
      style={{
        backgroundImage: "url('/images/pet care guide.jpg'), linear-gradient(to bottom right, rgb(240 253 244), rgb(255 255 255), rgb(239 246 255))",
        backgroundSize: 'cover, cover',
        backgroundPosition: 'center, center',
        backgroundRepeat: 'no-repeat, no-repeat',
        backgroundAttachment: 'fixed, scroll'
      }}
    >
      {/* Overlay for better content readability */}
      <div className="absolute inset-0 bg-gradient-to-br from-green-50/40 via-white/60 to-blue-50/40"></div>
      <div className="relative z-10 min-h-screen">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-12 relative">
          <div className="absolute inset-0 bg-gradient-to-r from-green-600/10 to-blue-600/10 rounded-3xl blur-3xl"></div>
          <div className="relative">
            <h1 className="text-5xl font-bold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent mb-4">
              🐾 Complete Pet Care Guide
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-6">
              Everything you need to know to keep your furry, feathered, or scaled friend happy and healthy
            </p>
            <div className="flex justify-center items-center gap-4 mt-6">
              <Badge className="bg-green-100 text-green-800 px-3 py-1">
                <Heart className="h-4 w-4 mr-1" />
                Expert Advice
              </Badge>
              <Badge className="bg-blue-100 text-blue-800 px-3 py-1">
                <BookOpen className="h-4 w-4 mr-1" />
                Comprehensive Guide
              </Badge>
              <Badge className="bg-purple-100 text-purple-800 px-3 py-1">
                <Award className="h-4 w-4 mr-1" />
                Vet Approved
              </Badge>
            </div>
          </div>
        </div>

        {/* Search and Filter */}
        <Card className="max-w-4xl mx-auto mb-8 shadow-xl border-0 bg-white/95 backdrop-blur-md">
          <CardContent className="p-6">
            <div className="flex gap-4 mb-4">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search care topics..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Button variant="outline" className="flex items-center gap-2">
                <Filter className="h-4 w-4" />
                Filter
              </Button>
            </div>
            <div className="flex gap-2 flex-wrap">
              <Button
                variant={selectedCategory === "all" ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedCategory("all")}
              >
                All Topics
              </Button>
              {careCategories.map((category) => (
                <Button
                  key={category.id}
                  variant={selectedCategory === category.id ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedCategory(category.id)}
                  className="flex items-center gap-1"
                >
                  <category.icon className="h-3 w-3" />
                  {category.title}
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="max-w-6xl mx-auto">
          <TabsList className="grid w-full grid-cols-4 mb-8">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="detailed">Detailed Guides</TabsTrigger>
            <TabsTrigger value="emergency">Emergency Care</TabsTrigger>
            <TabsTrigger value="resources">Resources</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-8">
            {/* Care Categories Grid */}
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredCategories.map((category) => (
                <Card key={category.id} className="hover:shadow-lg transition-shadow cursor-pointer group">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div className={`p-3 rounded-lg ${category.color}`}>
                        <category.icon className="h-6 w-6" />
                      </div>
                      <ChevronRight className="h-5 w-5 text-gray-400 group-hover:text-gray-600 transition-colors" />
                    </div>
                    <CardTitle className="text-lg">{category.title}</CardTitle>
                    <CardDescription>{category.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Button variant="outline" className="w-full">
                      Learn More
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Quick Tips */}
            <div className="grid lg:grid-cols-3 gap-6">
              {quickTips.map((section, index) => (
                <Card key={index}>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Zap className="h-5 w-5 text-yellow-500" />
                      {section.category}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2">
                      {section.tips.map((tip, tipIndex) => (
                        <li key={tipIndex} className="flex items-start gap-2 text-sm">
                          <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                          {tip}
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="detailed" className="space-y-8">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">Detailed Care Guides</h2>
              <p className="text-gray-600 max-w-2xl mx-auto">
                In-depth information for each aspect of pet care, from basic needs to advanced topics.
              </p>
            </div>
            
            {/* Detailed guides will be added here */}
            <div className="grid gap-6">
              {careCategories.map((category) => (
                <Card key={category.id} className="p-6">
                  <div className="flex items-start gap-4">
                    <div className={`p-3 rounded-lg ${category.color}`}>
                      <category.icon className="h-6 w-6" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-xl font-semibold mb-2">{category.title}</h3>
                      <p className="text-gray-600 mb-4">{category.description}</p>
                      <div className="flex gap-2">
                        <Button>Read Full Guide</Button>
                        <Button variant="outline">
                          <Download className="h-4 w-4 mr-2" />
                          Download PDF
                        </Button>
                      </div>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="emergency" className="space-y-8">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">Emergency Care Information</h2>
              <p className="text-gray-600 max-w-2xl mx-auto">
                Essential information for pet emergencies and important contact numbers.
              </p>
            </div>

            {/* Emergency Contacts */}
            <Card className="bg-red-50 border-red-200">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-red-800">
                  <Phone className="h-5 w-5" />
                  Emergency Contacts
                </CardTitle>
                <CardDescription className="text-red-600">
                  Keep these numbers easily accessible at all times
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-3 gap-4">
                  {emergencyContacts.map((contact, index) => (
                    <div key={index} className="bg-white p-4 rounded-lg border border-red-200">
                      <h4 className="font-semibold text-gray-900">{contact.name}</h4>
                      <p className="text-lg font-mono text-red-600 my-2">{contact.phone}</p>
                      <p className="text-sm text-gray-600">{contact.description}</p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Emergency Signs */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5 text-orange-500" />
                  Emergency Warning Signs
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-semibold mb-3 text-red-600">Immediate Emergency</h4>
                    <ul className="space-y-2">
                      <li className="flex items-start gap-2">
                        <AlertTriangle className="h-4 w-4 text-red-500 mt-0.5" />
                        <span className="text-sm">Difficulty breathing or choking</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <AlertTriangle className="h-4 w-4 text-red-500 mt-0.5" />
                        <span className="text-sm">Unconsciousness or collapse</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <AlertTriangle className="h-4 w-4 text-red-500 mt-0.5" />
                        <span className="text-sm">Severe bleeding or trauma</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <AlertTriangle className="h-4 w-4 text-red-500 mt-0.5" />
                        <span className="text-sm">Suspected poisoning</span>
                      </li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-3 text-orange-600">Urgent Care Needed</h4>
                    <ul className="space-y-2">
                      <li className="flex items-start gap-2">
                        <Info className="h-4 w-4 text-orange-500 mt-0.5" />
                        <span className="text-sm">Persistent vomiting or diarrhea</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <Info className="h-4 w-4 text-orange-500 mt-0.5" />
                        <span className="text-sm">Loss of appetite for 24+ hours</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <Info className="h-4 w-4 text-orange-500 mt-0.5" />
                        <span className="text-sm">Lethargy or unusual behavior</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <Info className="h-4 w-4 text-orange-500 mt-0.5" />
                        <span className="text-sm">High fever or very low temperature</span>
                      </li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="resources" className="space-y-8">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">Additional Resources</h2>
              <p className="text-gray-600 max-w-2xl mx-auto">
                Helpful tools, calculators, and external resources for pet care.
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Calculator className="h-5 w-5" />
                    Pet Care Calculators
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    <li><Link href="#" className="text-blue-600 hover:underline">Food Portion Calculator</Link></li>
                    <li><Link href="#" className="text-blue-600 hover:underline">Exercise Needs Calculator</Link></li>
                    <li><Link href="#" className="text-blue-600 hover:underline">Age Converter</Link></li>
                  </ul>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    Downloadable Guides
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    <li><Link href="#" className="text-blue-600 hover:underline">New Pet Checklist</Link></li>
                    <li><Link href="#" className="text-blue-600 hover:underline">Emergency Care Guide</Link></li>
                    <li><Link href="#" className="text-blue-600 hover:underline">Training Schedule Template</Link></li>
                  </ul>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Users className="h-5 w-5" />
                    Community Support
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    <li><Link href="#" className="text-blue-600 hover:underline">Pet Care Forum</Link></li>
                    <li><Link href="#" className="text-blue-600 hover:underline">Local Pet Groups</Link></li>
                    <li><Link href="#" className="text-blue-600 hover:underline">Expert Q&A</Link></li>
                  </ul>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>

        {/* Call to Action */}
        <Card className="max-w-4xl mx-auto mt-12 bg-gradient-to-r from-green-600 to-blue-600 text-white border-0">
          <CardContent className="p-8 text-center">
            <h3 className="text-2xl font-bold mb-4">Need Personalized Advice?</h3>
            <p className="text-green-100 mb-6">
              Our team of veterinary experts is here to help with specific questions about your pet's care.
            </p>
            <div className="flex justify-center gap-4">
              <Button variant="secondary">
                <Phone className="h-4 w-4 mr-2" />
                Contact Expert
              </Button>
              <Button variant="outline" className="bg-transparent border-white text-white hover:bg-white hover:text-green-600">
                <Calendar className="h-4 w-4 mr-2" />
                Schedule Consultation
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
      </div>
    </div>
  )
}
