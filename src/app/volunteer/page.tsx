"use client"

import { useState } from "react"
import { useSession } from "next-auth/react"
import { useRout<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  Users, 
  Heart, 
  Calendar, 
  Clock, 
  Award, 
  CheckCircle,
  ArrowRight,
  Dog,
  Car,
  Camera,
  Wrench,
  BookOpen,
  Phone
} from "lucide-react"
import Link from "next/link"
import { toast } from "react-hot-toast"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { volunteerApplicationSchema, type VolunteerApplicationInput } from "@/lib/validations"

const VOLUNTEER_OPPORTUNITIES = [
  {
    icon: Dog,
    title: "Animal Care",
    description: "Daily care, feeding, cleaning, and socialization",
    time: "2-4 hours",
    frequency: "Weekly",
    color: "bg-blue-100 text-blue-600"
  },
  {
    icon: Car,
    title: "Transport",
    description: "Transport pets to vet visits, events, and foster homes",
    time: "1-3 hours",
    frequency: "As needed",
    color: "bg-green-100 text-green-600"
  },
  {
    icon: Users,
    title: "Adoption Events",
    description: "Help at adoption events and community outreach",
    time: "3-6 hours",
    frequency: "Weekends",
    color: "bg-purple-100 text-purple-600"
  },
  {
    icon: Camera,
    title: "Photography",
    description: "Take photos and videos of pets for adoption profiles",
    time: "2-3 hours",
    frequency: "Flexible",
    color: "bg-pink-100 text-pink-600"
  },
  {
    icon: Wrench,
    title: "Facility Maintenance",
    description: "Help with repairs, cleaning, and facility improvements",
    time: "2-4 hours",
    frequency: "Monthly",
    color: "bg-orange-100 text-orange-600"
  },
  {
    icon: BookOpen,
    title: "Administrative",
    description: "Data entry, phone calls, and office support",
    time: "2-4 hours",
    frequency: "Weekly",
    color: "bg-indigo-100 text-indigo-600"
  }
]

export default function VolunteerPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [showApplication, setShowApplication] = useState(false)
  const [loading, setLoading] = useState(false)

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
  } = useForm<VolunteerApplicationInput>({
    resolver: zodResolver(volunteerApplicationSchema),
    defaultValues: {
      skills: [],
      interests: [],
      preferredActivities: [],
      availability: {
        monday: [],
        tuesday: [],
        wednesday: [],
        thursday: [],
        friday: [],
        saturday: [],
        sunday: [],
      },
    },
  })

  const watchedValues = watch()

  const onSubmit = async (data: VolunteerApplicationInput) => {
    if (!session) {
      toast.error("Please sign in to submit a volunteer application")
      return
    }

    setLoading(true)
    try {
      const response = await fetch("/api/volunteer/applications", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      })

      if (response.ok) {
        toast.success("Volunteer application submitted successfully!")
        router.push("/dashboard")
      } else {
        const errorData = await response.json()
        toast.error(errorData.error || "Failed to submit application")
      }
    } catch (error) {
      toast.error("Something went wrong. Please try again.")
    } finally {
      setLoading(false)
    }
  }

  const handleActivityToggle = (activity: string) => {
    const current = watchedValues.preferredActivities || []
    const updated = current.includes(activity)
      ? current.filter(a => a !== activity)
      : [...current, activity]
    setValue("preferredActivities", updated)
  }

  return (
    <div
      className="min-h-screen bg-gradient-to-br from-green-50 to-blue-100"
      style={{
        backgroundImage: "url('/images/volunteer with us page .jpg'), linear-gradient(to bottom right, rgb(240 253 244), rgb(219 234 254))",
        backgroundSize: 'cover, cover',
        backgroundPosition: 'center, center',
        backgroundRepeat: 'no-repeat, no-repeat',
        backgroundAttachment: 'fixed, scroll'
      }}
    >
      {/* Overlay for better content readability */}
      <div className="absolute inset-0 bg-gradient-to-br from-green-50/50 to-blue-100/50"></div>
      <div className="relative z-10 min-h-screen">
      <div className="container mx-auto px-4 py-12">
        {!showApplication ? (
          <>
            {/* Hero Section */}
            <div className="text-center mb-16">
              <div className="inline-flex items-center justify-center w-20 h-20 bg-green-100 rounded-full mb-6">
                <Users className="h-10 w-10 text-green-600" />
              </div>
              <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                Volunteer With Us
              </h1>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
                Join our amazing team of volunteers and make a direct impact on the lives of animals in need. 
                Every hour you give helps save lives and find forever homes.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                {session ? (
                  <Button 
                    size="lg" 
                    onClick={() => setShowApplication(true)}
                    className="bg-green-600 hover:bg-green-700"
                  >
                    Apply to Volunteer
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                ) : (
                  <Link href="/auth/signin?callbackUrl=/volunteer">
                    <Button size="lg" className="bg-green-600 hover:bg-green-700">
                      Sign In to Apply
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  </Link>
                )}
                <Button variant="outline" size="lg">
                  Learn More
                </Button>
              </div>
            </div>

            {/* Volunteer Opportunities */}
            <div className="mb-16">
              <h2 className="text-3xl font-bold text-center text-gray-900 mb-8">
                Volunteer Opportunities
              </h2>
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                {VOLUNTEER_OPPORTUNITIES.map((opportunity, index) => {
                  const Icon = opportunity.icon
                  return (
                    <Card key={index} className="hover:shadow-lg transition-shadow">
                      <CardHeader>
                        <div className={`inline-flex items-center justify-center w-12 h-12 rounded-lg mb-4 ${opportunity.color}`}>
                          <Icon className="h-6 w-6" />
                        </div>
                        <CardTitle className="text-xl">{opportunity.title}</CardTitle>
                        <CardDescription>{opportunity.description}</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="flex justify-between items-center text-sm">
                          <div className="flex items-center">
                            <Clock className="h-4 w-4 text-gray-400 mr-1" />
                            <span>{opportunity.time}</span>
                          </div>
                          <Badge variant="outline">{opportunity.frequency}</Badge>
                        </div>
                      </CardContent>
                    </Card>
                  )
                })}
              </div>
            </div>

            {/* Benefits */}
            <div className="grid md:grid-cols-3 gap-8 mb-16">
              <Card className="text-center">
                <CardHeader>
                  <div className="mx-auto mb-4 p-4 bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center">
                    <Heart className="h-8 w-8 text-blue-600" />
                  </div>
                  <CardTitle>Make a Difference</CardTitle>
                  <CardDescription>
                    Directly impact the lives of animals and help them find loving homes
                  </CardDescription>
                </CardHeader>
              </Card>

              <Card className="text-center">
                <CardHeader>
                  <div className="mx-auto mb-4 p-4 bg-purple-100 rounded-full w-16 h-16 flex items-center justify-center">
                    <Users className="h-8 w-8 text-purple-600" />
                  </div>
                  <CardTitle>Build Community</CardTitle>
                  <CardDescription>
                    Meet like-minded people and build lasting friendships
                  </CardDescription>
                </CardHeader>
              </Card>

              <Card className="text-center">
                <CardHeader>
                  <div className="mx-auto mb-4 p-4 bg-green-100 rounded-full w-16 h-16 flex items-center justify-center">
                    <Award className="h-8 w-8 text-green-600" />
                  </div>
                  <CardTitle>Gain Experience</CardTitle>
                  <CardDescription>
                    Develop new skills and gain valuable experience working with animals
                  </CardDescription>
                </CardHeader>
              </Card>
            </div>

            {/* Requirements */}
            <Card className="mb-16">
              <CardHeader>
                <CardTitle className="text-2xl text-center">Volunteer Requirements</CardTitle>
                <CardDescription className="text-center">
                  Basic requirements to join our volunteer team
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div className="flex items-center">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                      <span>Be at least 16 years old (under 18 with parent consent)</span>
                    </div>
                    <div className="flex items-center">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                      <span>Complete volunteer orientation</span>
                    </div>
                    <div className="flex items-center">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                      <span>Commit to at least 2 hours per month</span>
                    </div>
                    <div className="flex items-center">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                      <span>Pass background check</span>
                    </div>
                  </div>
                  <div className="space-y-4">
                    <div className="flex items-center">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                      <span>Follow safety protocols</span>
                    </div>
                    <div className="flex items-center">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                      <span>Maintain confidentiality</span>
                    </div>
                    <div className="flex items-center">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                      <span>Be reliable and punctual</span>
                    </div>
                    <div className="flex items-center">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                      <span>Have a positive attitude</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Call to Action */}
            <div className="text-center">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Ready to Get Started?
              </h2>
              <p className="text-xl text-gray-600 mb-8">
                Join our volunteer team and start making a difference today.
              </p>
              {session ? (
                <Button 
                  size="lg" 
                  onClick={() => setShowApplication(true)}
                  className="bg-green-600 hover:bg-green-700"
                >
                  Start Volunteer Application
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              ) : (
                <Link href="/auth/signin?callbackUrl=/volunteer">
                  <Button size="lg" className="bg-green-600 hover:bg-green-700">
                    Sign In to Apply
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </Link>
              )}
            </div>
          </>
        ) : (
          /* Volunteer Application Form */
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-8">
              <h1 className="text-3xl font-bold text-gray-900 mb-4">
                Volunteer Application
              </h1>
              <p className="text-gray-600">
                Tell us about your interests, skills, and availability so we can find the perfect volunteer role for you.
              </p>
            </div>

            <form onSubmit={handleSubmit(onSubmit)}>
              <Card>
                <CardHeader>
                  <CardTitle>Volunteer Information</CardTitle>
                  <CardDescription>
                    Help us understand your interests and availability
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Preferred Activities */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-3">
                      Preferred Activities * (Select at least one)
                    </label>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      {VOLUNTEER_OPPORTUNITIES.map((opportunity) => (
                        <label key={opportunity.title} className="flex items-start p-3 border rounded-lg cursor-pointer hover:bg-gray-50">
                          <input
                            type="checkbox"
                            checked={watchedValues.preferredActivities?.includes(opportunity.title) || false}
                            onChange={() => handleActivityToggle(opportunity.title)}
                            className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded mt-1"
                          />
                          <div className="ml-3">
                            <div className="font-medium text-sm">{opportunity.title}</div>
                            <div className="text-xs text-gray-600">{opportunity.description}</div>
                          </div>
                        </label>
                      ))}
                    </div>
                    {errors.preferredActivities && (
                      <p className="text-red-500 text-sm mt-1">{errors.preferredActivities.message}</p>
                    )}
                  </div>

                  {/* Experience */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Volunteer Experience
                      </label>
                      <textarea
                        {...register("volunteerExperience")}
                        className="w-full p-2 border border-gray-300 rounded-md"
                        rows={3}
                        placeholder="Describe any previous volunteer experience..."
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Animal Experience
                      </label>
                      <textarea
                        {...register("animalExperience")}
                        className="w-full p-2 border border-gray-300 rounded-md"
                        rows={3}
                        placeholder="Describe your experience with animals..."
                      />
                    </div>
                  </div>

                  {/* Skills and Availability */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Special Skills
                      </label>
                      <textarea
                        {...register("specialSkills")}
                        className="w-full p-2 border border-gray-300 rounded-md"
                        rows={3}
                        placeholder="Photography, carpentry, veterinary, etc..."
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Maximum Hours Per Week
                      </label>
                      <Input
                        type="number"
                        min="1"
                        max="40"
                        {...register("maxHoursPerWeek", { valueAsNumber: true })}
                        placeholder="How many hours can you volunteer per week?"
                      />
                    </div>
                  </div>

                  {/* Emergency Contact */}
                  <div className="border-t pt-6">
                    <h3 className="text-lg font-semibold mb-4">Emergency Contact *</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Name *
                        </label>
                        <Input
                          {...register("emergencyContactName")}
                          placeholder="Emergency contact name"
                        />
                        {errors.emergencyContactName && (
                          <p className="text-red-500 text-sm mt-1">{errors.emergencyContactName.message}</p>
                        )}
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Phone Number *
                        </label>
                        <Input
                          type="tel"
                          {...register("emergencyContactPhone")}
                          placeholder="Emergency contact phone"
                        />
                        {errors.emergencyContactPhone && (
                          <p className="text-red-500 text-sm mt-1">{errors.emergencyContactPhone.message}</p>
                        )}
                      </div>
                    </div>
                  </div>
                </CardContent>

                <div className="flex justify-between items-center p-6 border-t">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setShowApplication(false)}
                  >
                    Back to Information
                  </Button>

                  <Button
                    type="submit"
                    disabled={loading}
                    className="bg-green-600 hover:bg-green-700"
                  >
                    {loading ? "Submitting..." : "Submit Volunteer Application"}
                  </Button>
                </div>
              </Card>
            </form>
          </div>
        )}
      </div>
      </div>
    </div>
  )
}
